/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
}

/* Animated background particles */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(-20px) rotate(240deg); }
}

.privacy-policy-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header styles */
.header {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);
  backdrop-filter: blur(10px);
  color: white;
  padding: 3rem 0;
  box-shadow: 0 8px 32px rgba(0,0,0,0.2);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.app-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
  background: linear-gradient(45deg, #fff, #e0e7ff, #fff);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-text 3s ease infinite;
  text-shadow: 0 0 30px rgba(255,255,255,0.5);
  position: relative;
  z-index: 1;
}

@keyframes gradient-text {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.subtitle {
  font-size: 1.4rem;
  opacity: 0.95;
  text-align: center;
  animation: fadeInUp 1s ease-out 0.5s both;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 0.95;
    transform: translateY(0);
  }
}

/* Main content styles */
.main-content {
  flex: 1;
  padding: 4rem 0;
  position: relative;
}

.policy-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 4rem;
  box-shadow:
    0 20px 40px rgba(0,0,0,0.1),
    0 0 0 1px rgba(255,255,255,0.2);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.8s ease-out;
  transition: all 0.3s ease;
}

.policy-content:hover {
  transform: translateY(-5px);
  box-shadow:
    0 30px 60px rgba(0,0,0,0.15),
    0 0 0 1px rgba(255,255,255,0.3);
}

.policy-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  background-size: 200% 100%;
  animation: gradient-border 3s ease infinite;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-border {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.policy-section {
  margin-bottom: 3rem;
  padding: 2rem;
  border-radius: 15px;
  background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.policy-section:hover {
  transform: translateX(-5px);
  background: linear-gradient(145deg, rgba(255,255,255,0.15), rgba(255,255,255,0.08));
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
}

.policy-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #667eea, #764ba2);
  transform: scaleY(0);
  transition: transform 0.3s ease;
  transform-origin: bottom;
}

.policy-section:hover::before {
  transform: scaleY(1);
}

.policy-section h2 {
  color: #667eea;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.8rem;
  border-bottom: 3px solid transparent;
  background: linear-gradient(90deg, #e9ecef, #e9ecef) padding-box,
              linear-gradient(90deg, #667eea, #764ba2) border-box;
  border-image: linear-gradient(90deg, #667eea, #764ba2) 1;
  position: relative;
  animation: fadeInRight 0.6s ease-out;
}

.policy-section h2::after {
  content: '';
  position: absolute;
  bottom: -3px;
  right: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.policy-section:hover h2::after {
  width: 100%;
}

.policy-section h3 {
  color: #495057;
  font-size: 1.4rem;
  margin: 2rem 0 1rem 0;
  position: relative;
  padding-right: 1rem;
}

.policy-section h3::before {
  content: '▶';
  position: absolute;
  right: -0.5rem;
  color: #667eea;
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.policy-section:hover h3::before {
  transform: translateX(-3px);
}

.policy-section p {
  margin-bottom: 1.2rem;
  line-height: 1.9;
  color: #555;
  animation: fadeIn 0.8s ease-out;
  transition: color 0.3s ease;
}

.policy-section:hover p {
  color: #333;
}

.policy-section ul {
  margin: 1.5rem 0;
  padding-right: 2.5rem;
}

.policy-section li {
  margin-bottom: 0.8rem;
  color: #555;
  position: relative;
  transition: all 0.3s ease;
  padding: 0.3rem 0;
}

.policy-section li::before {
  content: '●';
  color: #667eea;
  position: absolute;
  right: -1.5rem;
  transition: all 0.3s ease;
}

.policy-section:hover li::before {
  color: #764ba2;
  transform: scale(1.2);
}

.policy-section li:hover {
  color: #333;
  transform: translateX(-3px);
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.contact-info {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  padding: 2rem;
  border-radius: 15px;
  border: 2px solid transparent;
  background-clip: padding-box;
  margin-top: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.contact-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  z-index: -1;
  margin: -2px;
  border-radius: inherit;
}

.contact-info:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
}

.contact-info p {
  margin-bottom: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.contact-info p:hover {
  color: #667eea;
  transform: translateX(-5px);
}

.contact-info strong {
  color: #667eea;
  font-weight: 600;
}

.last-updated {
  text-align: center;
  margin-top: 4rem;
  padding-top: 2.5rem;
  border-top: 2px solid transparent;
  background: linear-gradient(90deg, transparent, #e9ecef, transparent) padding-box,
              linear-gradient(90deg, transparent, #667eea, transparent) border-box;
  color: #6c757d;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Footer styles */
.footer {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  text-align: center;
  padding: 3rem 0;
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #667eea, #764ba2, transparent);
  animation: footerLine 3s ease-in-out infinite;
}

@keyframes footerLine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: -100%; }
}

.footer p {
  font-size: 1.1rem;
  margin: 0;
  animation: fadeInUp 1s ease-out 1s both;
}

/* Scroll animations */
.policy-section {
  opacity: 0;
  transform: translateY(30px);
  animation: scrollFadeIn 0.6s ease-out forwards;
}

.policy-section:nth-child(1) { animation-delay: 0.1s; }
.policy-section:nth-child(2) { animation-delay: 0.2s; }
.policy-section:nth-child(3) { animation-delay: 0.3s; }
.policy-section:nth-child(4) { animation-delay: 0.4s; }
.policy-section:nth-child(5) { animation-delay: 0.5s; }
.policy-section:nth-child(6) { animation-delay: 0.6s; }
.policy-section:nth-child(7) { animation-delay: 0.7s; }
.policy-section:nth-child(8) { animation-delay: 0.8s; }

@keyframes scrollFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading animation */
.policy-content {
  position: relative;
}

.policy-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transform-origin: left;
  animation: loadingBar 2s ease-out 0.5s forwards;
}

@keyframes loadingBar {
  to { transform: scaleX(1); }
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .app-title {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .policy-content {
    padding: 2.5rem 1.5rem;
    border-radius: 15px;
  }

  .policy-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .policy-section h2 {
    font-size: 1.6rem;
  }

  .policy-section h3 {
    font-size: 1.2rem;
  }

  .policy-section ul {
    padding-right: 1.5rem;
  }

  .contact-info {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 2rem 0;
  }

  .app-title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .main-content {
    padding: 2rem 0;
  }

  .policy-content {
    padding: 1.5rem 1rem;
    border-radius: 12px;
  }

  .policy-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .policy-section h2 {
    font-size: 1.4rem;
  }

  .policy-section h3 {
    font-size: 1.1rem;
  }

  .contact-info {
    padding: 1rem;
  }

  .footer {
    padding: 2rem 0;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection styling */
::selection {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

::-moz-selection {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

/* Link styling */
a {
  transition: all 0.3s ease;
  position: relative;
}

a:hover {
  transform: translateY(-2px);
  text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

a:hover::after {
  width: 100%;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #764ba2, #667eea);
}

/* Loading state */
.privacy-policy-container:not(.loaded) .policy-content {
  opacity: 0;
  transform: translateY(50px);
}

.privacy-policy-container.loaded .policy-content {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s ease-out;
}

/* Floating elements animation */
@keyframes float-gentle {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.policy-section:nth-child(even) {
  animation: float-gentle 6s ease-in-out infinite;
  animation-delay: 1s;
}

.policy-section:nth-child(odd) {
  animation: float-gentle 6s ease-in-out infinite;
  animation-delay: 3s;
}
