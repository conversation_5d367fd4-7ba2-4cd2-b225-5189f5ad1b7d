/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
  direction: rtl;
  text-align: right;
}

.privacy-policy-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header styles */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-align: center;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  text-align: center;
}

/* Main content styles */
.main-content {
  flex: 1;
  padding: 3rem 0;
}

.policy-content {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.policy-section {
  margin-bottom: 2.5rem;
}

.policy-section h2 {
  color: #667eea;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.policy-section h3 {
  color: #495057;
  font-size: 1.3rem;
  margin: 1.5rem 0 0.8rem 0;
}

.policy-section p {
  margin-bottom: 1rem;
  line-height: 1.8;
  color: #555;
}

.policy-section ul {
  margin: 1rem 0;
  padding-right: 2rem;
}

.policy-section li {
  margin-bottom: 0.5rem;
  color: #555;
}

.contact-info {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-right: 4px solid #667eea;
  margin-top: 1rem;
}

.contact-info p {
  margin-bottom: 0.5rem;
}

.last-updated {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
}

/* Footer styles */
.footer {
  background: #343a40;
  color: white;
  text-align: center;
  padding: 2rem 0;
  margin-top: auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .app-title {
    font-size: 2rem;
  }

  .policy-content {
    padding: 2rem 1.5rem;
  }

  .policy-section h2 {
    font-size: 1.5rem;
  }

  .policy-section ul {
    padding-right: 1.5rem;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1.8rem;
  }

  .policy-content {
    padding: 1.5rem 1rem;
  }

  .policy-section h2 {
    font-size: 1.3rem;
  }

  .contact-info {
    padding: 1rem;
  }
}
